'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '../ui/card';
import { AssessmentScores, getScoreInterpretation } from '../../types/assessment-results';
import { getDominantRiasecType, getTopViaStrengths } from '../../utils/assessment-calculations';
import { TrendingUp, BarChart3, Brain, Palette } from 'lucide-react';

interface VisualSummaryProps {
  scores: AssessmentScores;
}

export default function VisualSummary({ scores }: VisualSummaryProps) {
  // Get top categories from each assessment type
  const dominantRiasec = getDominantRiasecType(scores.riasec);
  const topViaStrengths = getTopViaStrengths(scores.viaIs, 1);

  // Get highest Big Five trait
  const oceanEntries = Object.entries(scores.ocean).sort(([,a], [,b]) => b - a);
  const topOceanTrait = oceanEntries[0];

  // Calculate meaningful life area scores based on assessment results
  // Using psychological research-backed mappings

  // HABITS: Personal discipline and self-regulation
  // Based on Conscientiousness + Self-Regulation + Perseverance
  const habitsScore = Math.round((
    scores.ocean.conscientiousness +
    (scores.viaIs.selfRegulation || 0) +
    (scores.viaIs.perseverance || 0)
  ) / 3);

  // WORK: Career aptitude and professional effectiveness
  // Based on dominant RIASEC + relevant Big Five traits + work-related VIA strengths
  const workScore = Math.round((
    scores.riasec[dominantRiasec.primary as keyof typeof scores.riasec] +
    scores.ocean.conscientiousness +
    (scores.viaIs.leadership || 0) +
    (scores.viaIs.perseverance || 0)
  ) / 4);

  // HOME: Family relationships and domestic harmony
  // Based on Agreeableness + emotional traits + family-oriented VIA strengths
  const homeScore = Math.round((
    scores.ocean.agreeableness +
    (100 - scores.ocean.neuroticism) + // Emotional stability (inverted neuroticism)
    (scores.viaIs.love || 0) +
    (scores.viaIs.kindness || 0) +
    (scores.viaIs.forgiveness || 0)
  ) / 5);

  // SOCIAL: Social relationships and community engagement
  // Based on Extraversion + Social intelligence + social VIA strengths
  const socialScore = Math.round((
    scores.ocean.extraversion +
    scores.ocean.agreeableness +
    (scores.viaIs.socialIntelligence || 0) +
    (scores.viaIs.teamwork || 0) +
    (scores.viaIs.fairness || 0)
  ) / 5);

  const topCategories = [
    {
      name: 'HABITS',
      shortName: 'HABITS',
      score: habitsScore,
      color: '#ff6b35', // Orange
      icon: BarChart3,
      type: 'Personal Development',
      description: 'Kedisiplinan dan pengaturan diri'
    },
    {
      name: 'WORK',
      shortName: 'WORK',
      score: workScore,
      color: '#ffb3a7', // Light orange/pink
      icon: Brain,
      type: 'Career & Professional',
      description: 'Kesesuaian dan efektivitas karir'
    },
    {
      name: 'HOME',
      shortName: 'HOME',
      score: homeScore,
      color: '#a855f7', // Purple
      icon: Palette,
      type: 'Family & Personal Life',
      description: 'Hubungan keluarga dan kehidupan pribadi'
    },
    {
      name: 'SOCIAL',
      shortName: 'SOCIAL',
      score: socialScore,
      color: '#06b6d4', // Cyan
      icon: TrendingUp,
      type: 'Social & Relationships',
      description: 'Hubungan sosial dan keterlibatan komunitas'
    }
  ];

  // Sort by score to show highest first
  const sortedCategories = topCategories.sort((a, b) => b.score - a.score);

  // Circular Progress Component
  const CircularProgress = ({
    percentage,
    color,
    size = 120,
    strokeWidth = 8,
    index = 0
  }: {
    percentage: number;
    color: string;
    size?: number;
    strokeWidth?: number;
    index?: number;
  }) => {
    const radius = (size - strokeWidth) / 2;
    const circumference = radius * 2 * Math.PI;
    const strokeDasharray = circumference;
    const strokeDashoffset = circumference - (percentage / 100) * circumference;

    // Calculate rotation for each ring to create the spiral effect
    const rotation = index * 30; // Rotate each ring by 30 degrees for better spacing

    return (
      <div className="absolute inset-0 flex items-center justify-center">
        <svg
          width={size}
          height={size}
          className="transform"
          style={{ transform: `rotate(${rotation - 90}deg)` }}
        >
          {/* Background circle - very light */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="#f8fafc"
            strokeWidth={strokeWidth}
            fill="transparent"
            opacity={0.5}
          />
          {/* Progress circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={color}
            strokeWidth={strokeWidth}
            fill="transparent"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            className="transition-all duration-1000 ease-out"
            style={{
              filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
            }}
          />
        </svg>
      </div>
    );
  };

  // Calculate overall performance
  const overallScore = Math.round(
    (sortedCategories.reduce((sum, cat) => sum + cat.score, 0)) / 4
  );

  return (
    <Card className="bg-white border-[#eaecf0]">
      <CardHeader className="pb-4">
        <div className="flex items-center gap-2">
          <div className="p-2 bg-[#e7eaff] rounded-lg">
            <TrendingUp className="w-6 h-6 text-[#6475e9]" />
          </div>
          <div>
            <CardTitle className="text-xl font-semibold text-[#1e1e1e]">
              Performance Summary
            </CardTitle>
            <p className="text-sm text-[#64707d]">
              Visualisasi kekuatan utama Anda
            </p>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Circular Progress Chart */}
        <div className="flex items-center justify-between">
          {/* Left side - Circular Chart */}
          <div className="relative flex items-center justify-center" style={{ width: '300px', height: '300px' }}>
            {/* Concentric circles */}
            {sortedCategories.map((category, index) => (
              <div
                key={index}
                className="absolute inset-0"
                style={{
                  zIndex: sortedCategories.length - index
                }}
              >
                <CircularProgress
                  percentage={category.score}
                  color={category.color}
                  size={280 - index * 40}
                  strokeWidth={20}
                  index={index}
                />
              </div>
            ))}
          </div>

          {/* Right side - Legend */}
          <div className="flex-1 ml-12 space-y-6">
            {sortedCategories.map((category, index) => (
              <div key={index} className="group">
                <div className="flex items-center gap-4">
                  <div
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: category.color }}
                  />
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div>
                        <span className="text-sm font-medium text-gray-500 uppercase tracking-wider">
                          {category.shortName}
                        </span>
                        <p className="text-xs text-gray-400 mt-1">
                          {category.description}
                        </p>
                      </div>
                      <span className="text-2xl font-bold" style={{ color: category.color }}>
                        {category.score}%
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Performance Insights */}
        <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
          <div className="flex items-start gap-3">
            <div className="p-2 bg-blue-100 rounded-lg flex-shrink-0">
              <TrendingUp className="w-4 h-4 text-blue-600" />
            </div>
            <div>
              <h4 className="text-sm font-semibold text-blue-900 mb-2">
                Performance Insight
              </h4>
              <p className="text-xs text-blue-700 leading-relaxed mb-3">
                {overallScore >= 80
                  ? "Anda menunjukkan performa yang sangat baik di berbagai aspek kehidupan. Kekuatan utama Anda terletak pada area yang ditampilkan di atas."
                  : overallScore >= 60
                  ? "Anda memiliki profil yang seimbang dengan beberapa area kekuatan yang menonjol. Fokuskan pengembangan pada area dengan skor tertinggi."
                  : "Anda memiliki potensi yang baik untuk dikembangkan. Pertimbangkan untuk fokus pada pengembangan area-area kekuatan yang sudah teridentifikasi."
                }
              </p>
              <div className="text-xs text-blue-600 bg-blue-100 rounded p-2">
                <p className="font-medium mb-1">Cara Perhitungan Skor:</p>
                <ul className="space-y-1 text-blue-700">
                  <li>• <strong>HABITS:</strong> Conscientiousness + Self-Regulation + Perseverance</li>
                  <li>• <strong>WORK:</strong> RIASEC dominan + Conscientiousness + Leadership + Perseverance</li>
                  <li>• <strong>HOME:</strong> Agreeableness + Stabilitas Emosi + Love + Kindness + Forgiveness</li>
                  <li>• <strong>SOCIAL:</strong> Extraversion + Agreeableness + Social Intelligence + Teamwork + Fairness</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
