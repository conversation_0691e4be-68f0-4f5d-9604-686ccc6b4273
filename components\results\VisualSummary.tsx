'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '../ui/card';
import { AssessmentScores, getScoreInterpretation } from '../../types/assessment-results';
import { getDominantRiasecType, getTopViaStrengths } from '../../utils/assessment-calculations';
import { TrendingUp, BarChart3, Brain, Palette } from 'lucide-react';

interface VisualSummaryProps {
  scores: AssessmentScores;
}

export default function VisualSummary({ scores }: VisualSummaryProps) {
  // Get top categories from each assessment type
  const dominantRiasec = getDominantRiasecType(scores.riasec);
  const topViaStrengths = getTopViaStrengths(scores.viaIs, 1);

  // Get highest Big Five trait
  const oceanEntries = Object.entries(scores.ocean).sort(([,a], [,b]) => b - a);
  const topOceanTrait = oceanEntries[0];

  // Create top 4 categories for visualization (matching the image style)
  // Map assessment data to life areas
  const topCategories = [
    {
      name: 'HABITS',
      shortName: 'HABITS',
      score: Math.round((scores.ocean.conscientiousness + topViaStrengths[0]?.score || 0) / 2),
      color: '#ff6b35', // Orange
      icon: BarChart3,
      type: 'Personal Development'
    },
    {
      name: 'WORK',
      shortName: 'WORK',
      score: scores.riasec[dominantRiasec.primary as keyof typeof scores.riasec],
      color: '#ffb3a7', // Light orange/pink
      icon: Brain,
      type: 'Career & Professional'
    },
    {
      name: 'HOME',
      shortName: 'HOME',
      score: Math.round((scores.ocean.agreeableness + scores.ocean.conscientiousness) / 2),
      color: '#a855f7', // Purple
      icon: Palette,
      type: 'Family & Personal Life'
    },
    {
      name: 'SOCIAL',
      shortName: 'SOCIAL',
      score: Math.round((scores.ocean.extraversion + scores.ocean.agreeableness) / 2),
      color: '#06b6d4', // Cyan
      icon: TrendingUp,
      type: 'Social & Relationships'
    }
  ];

  // Sort by score to show highest first
  const sortedCategories = topCategories.sort((a, b) => b.score - a.score);

  // Circular Progress Component
  const CircularProgress = ({
    percentage,
    color,
    size = 120,
    strokeWidth = 8,
    index = 0
  }: {
    percentage: number;
    color: string;
    size?: number;
    strokeWidth?: number;
    index?: number;
  }) => {
    const radius = (size - strokeWidth) / 2;
    const circumference = radius * 2 * Math.PI;
    const strokeDasharray = circumference;
    const strokeDashoffset = circumference - (percentage / 100) * circumference;

    // Calculate rotation for each ring to create the spiral effect
    const rotation = index * 30; // Rotate each ring by 30 degrees for better spacing

    return (
      <div className="absolute inset-0 flex items-center justify-center">
        <svg
          width={size}
          height={size}
          className="transform"
          style={{ transform: `rotate(${rotation - 90}deg)` }}
        >
          {/* Background circle - very light */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="#f8fafc"
            strokeWidth={strokeWidth}
            fill="transparent"
            opacity={0.5}
          />
          {/* Progress circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={color}
            strokeWidth={strokeWidth}
            fill="transparent"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            className="transition-all duration-1000 ease-out"
            style={{
              filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
            }}
          />
        </svg>
      </div>
    );
  };

  // Calculate overall performance
  const overallScore = Math.round(
    (sortedCategories.reduce((sum, cat) => sum + cat.score, 0)) / 4
  );

  return (
    <Card className="bg-white border-[#eaecf0]">
      <CardHeader className="pb-4">
        <div className="flex items-center gap-2">
          <div className="p-2 bg-[#e7eaff] rounded-lg">
            <TrendingUp className="w-6 h-6 text-[#6475e9]" />
          </div>
          <div>
            <CardTitle className="text-xl font-semibold text-[#1e1e1e]">
              Performance Summary
            </CardTitle>
            <p className="text-sm text-[#64707d]">
              Visualisasi kekuatan utama Anda
            </p>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Circular Progress Chart */}
        <div className="flex items-center justify-between">
          {/* Left side - Circular Chart */}
          <div className="relative flex items-center justify-center" style={{ width: '300px', height: '300px' }}>
            {/* Concentric circles */}
            {sortedCategories.map((category, index) => (
              <div
                key={index}
                className="absolute inset-0"
                style={{
                  zIndex: sortedCategories.length - index
                }}
              >
                <CircularProgress
                  percentage={category.score}
                  color={category.color}
                  size={280 - index * 40}
                  strokeWidth={20}
                  index={index}
                />
              </div>
            ))}
          </div>

          {/* Right side - Legend */}
          <div className="flex-1 ml-12 space-y-6">
            {sortedCategories.map((category, index) => (
              <div key={index} className="flex items-center gap-4">
                <div
                  className="w-4 h-4 rounded-full"
                  style={{ backgroundColor: category.color }}
                />
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-500 uppercase tracking-wider">
                      {category.shortName}
                    </span>
                    <span className="text-2xl font-bold" style={{ color: category.color }}>
                      {category.score}%
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Performance Insights */}
        <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
          <div className="flex items-start gap-3">
            <div className="p-2 bg-blue-100 rounded-lg flex-shrink-0">
              <TrendingUp className="w-4 h-4 text-blue-600" />
            </div>
            <div>
              <h4 className="text-sm font-semibold text-blue-900 mb-1">
                Performance Insight
              </h4>
              <p className="text-xs text-blue-700 leading-relaxed">
                {overallScore >= 80
                  ? "Anda menunjukkan performa yang sangat baik di berbagai aspek kepribadian. Kekuatan utama Anda terletak pada area yang ditampilkan di atas."
                  : overallScore >= 60
                  ? "Anda memiliki profil kepribadian yang seimbang dengan beberapa area kekuatan yang menonjol. Fokuskan pengembangan pada area dengan skor tertinggi."
                  : "Anda memiliki potensi yang baik untuk dikembangkan. Pertimbangkan untuk fokus pada pengembangan area-area kekuatan yang sudah teridentifikasi."
                }
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
