'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '../ui/card';
import { AssessmentScores, getScoreInterpretation } from '../../types/assessment-results';
import { getDominantRiasecType, getTopViaStrengths } from '../../utils/assessment-calculations';
import { TrendingUp, BarChart3, Brain, Palette } from 'lucide-react';

interface VisualSummaryProps {
  scores: AssessmentScores;
}

export default function VisualSummary({ scores }: VisualSummaryProps) {
  // Get top categories from each assessment type
  const dominantRiasec = getDominantRiasecType(scores.riasec);
  const topViaStrengths = getTopViaStrengths(scores.viaIs, 1);

  // Get highest Big Five trait
  const oceanEntries = Object.entries(scores.ocean).sort(([,a], [,b]) => b - a);
  const topOceanTrait = oceanEntries[0];

  // Calculate meaningful life area scores based on assessment results
  // Using psychological research-backed mappings

  // HABITS: Personal discipline and self-regulation
  // Based on Conscientiousness + Self-Regulation + Perseverance
  const habitsScore = Math.round((
    scores.ocean.conscientiousness +
    (scores.viaIs.selfRegulation || 0) +
    (scores.viaIs.perseverance || 0)
  ) / 3);

  // WORK: Career aptitude and professional effectiveness
  // Based on dominant RIASEC + relevant Big Five traits + work-related VIA strengths
  const workScore = Math.round((
    scores.riasec[dominantRiasec.primary as keyof typeof scores.riasec] +
    scores.ocean.conscientiousness +
    (scores.viaIs.leadership || 0) +
    (scores.viaIs.perseverance || 0)
  ) / 4);

  // HOME: Family relationships and domestic harmony
  // Based on Agreeableness + emotional traits + family-oriented VIA strengths
  const homeScore = Math.round((
    scores.ocean.agreeableness +
    (100 - scores.ocean.neuroticism) + // Emotional stability (inverted neuroticism)
    (scores.viaIs.love || 0) +
    (scores.viaIs.kindness || 0) +
    (scores.viaIs.forgiveness || 0)
  ) / 5);

  // SOCIAL: Social relationships and community engagement
  // Based on Extraversion + Social intelligence + social VIA strengths
  const socialScore = Math.round((
    scores.ocean.extraversion +
    scores.ocean.agreeableness +
    (scores.viaIs.socialIntelligence || 0) +
    (scores.viaIs.teamwork || 0) +
    (scores.viaIs.fairness || 0)
  ) / 5);

  const topCategories = [
    {
      name: 'HABITS',
      shortName: 'HABITS',
      score: habitsScore,
      color: '#ff6b35', // Orange
      icon: BarChart3,
      type: 'Personal Development',
      description: 'Kedisiplinan dan pengaturan diri'
    },
    {
      name: 'WORK',
      shortName: 'WORK',
      score: workScore,
      color: '#ffb3a7', // Light orange/pink
      icon: Brain,
      type: 'Career & Professional',
      description: 'Kesesuaian dan efektivitas karir'
    },
    {
      name: 'HOME',
      shortName: 'HOME',
      score: homeScore,
      color: '#a855f7', // Purple
      icon: Palette,
      type: 'Family & Personal Life',
      description: 'Hubungan keluarga dan kehidupan pribadi'
    },
    {
      name: 'SOCIAL',
      shortName: 'SOCIAL',
      score: socialScore,
      color: '#06b6d4', // Cyan
      icon: TrendingUp,
      type: 'Social & Relationships',
      description: 'Hubungan sosial dan keterlibatan komunitas'
    }
  ];

  // Sort by score to show highest first
  const sortedCategories = topCategories.sort((a, b) => b.score - a.score);

  // State for hover effects
  const [hoveredIndex, setHoveredIndex] = React.useState<number | null>(null);

  // Circular Progress Component with hover effects
  const CircularProgress = ({
    percentage,
    color,
    size = 120,
    strokeWidth = 8,
    index = 0,
    category,
    isHovered = false
  }: {
    percentage: number;
    color: string;
    size?: number;
    strokeWidth?: number;
    index?: number;
    category: any;
    isHovered?: boolean;
  }) => {
    const radius = (size - strokeWidth) / 2;
    const circumference = radius * 2 * Math.PI;
    const strokeDasharray = circumference;
    const strokeDashoffset = circumference - (percentage / 100) * circumference;

    // Calculate rotation for each ring to create the spiral effect
    const rotation = index * 30; // Rotate each ring by 30 degrees for better spacing

    return (
      <div
        className="absolute inset-0 flex items-center justify-center cursor-pointer"
        onMouseEnter={() => setHoveredIndex(index)}
        onMouseLeave={() => setHoveredIndex(null)}
      >
        <svg
          width={size}
          height={size}
          className="transform transition-all duration-300"
          style={{
            transform: `rotate(${rotation - 90}deg) scale(${isHovered ? 1.05 : 1})`,
            filter: isHovered ? 'drop-shadow(0 4px 8px rgba(0,0,0,0.2))' : 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
          }}
        >
          {/* Background circle - very light */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="#f8fafc"
            strokeWidth={strokeWidth}
            fill="transparent"
            opacity={0.5}
          />
          {/* Progress circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={color}
            strokeWidth={isHovered ? strokeWidth + 2 : strokeWidth}
            fill="transparent"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            className="transition-all duration-300 ease-out"
            style={{
              opacity: isHovered ? 1 : 0.9
            }}
          />
        </svg>

        {/* Tooltip on hover */}
        {isHovered && (
          <div
            className="absolute z-50 bg-white rounded-lg shadow-xl border border-gray-200 p-4 pointer-events-none animate-in fade-in duration-200"
            style={{
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              minWidth: '220px',
              maxWidth: '280px',
              boxShadow: '0 10px 25px rgba(0,0,0,0.15)'
            }}
          >
            <div className="text-center">
              <div className="flex items-center justify-center gap-2 mb-3">
                <div
                  className="w-4 h-4 rounded-full shadow-sm"
                  style={{ backgroundColor: color }}
                />
                <span className="font-semibold text-gray-800 text-base">
                  {category.shortName}
                </span>
              </div>
              <div className="text-3xl font-bold mb-2" style={{ color: color }}>
                {percentage}%
              </div>
              <div className="text-sm text-gray-600 mb-2 leading-relaxed">
                {category.description}
              </div>
              <div className="text-xs text-gray-500 font-medium">
                {category.type}
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  // Calculate overall performance
  const overallScore = Math.round(
    (sortedCategories.reduce((sum, cat) => sum + cat.score, 0)) / 4
  );

  return (
    <Card className="bg-white border-[#eaecf0]">
      <CardHeader className="pb-4">
        <div className="flex items-center gap-2">
          <div className="p-2 bg-[#e7eaff] rounded-lg">
            <TrendingUp className="w-6 h-6 text-[#6475e9]" />
          </div>
          <div>
            <CardTitle className="text-xl font-semibold text-[#1e1e1e]">
              Performance Summary
            </CardTitle>
            <p className="text-sm text-[#64707d]">
              Visualisasi kekuatan utama Anda
            </p>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Circular Progress Chart */}
        <div className="flex items-center justify-between">
          {/* Left side - Circular Chart */}
          <div
            className="relative flex items-center justify-center transition-all duration-300"
            style={{
              width: '300px',
              height: '300px',
              filter: hoveredIndex !== null ? 'brightness(1.05)' : 'brightness(1)'
            }}
          >
            {/* Background glow effect when hovering */}
            {hoveredIndex !== null && (
              <div
                className="absolute inset-0 rounded-full opacity-20 transition-all duration-500"
                style={{
                  background: `radial-gradient(circle, ${sortedCategories[hoveredIndex].color}20 0%, transparent 70%)`,
                  transform: 'scale(1.2)'
                }}
              />
            )}

            {/* Concentric circles */}
            {sortedCategories.map((category, index) => (
              <div
                key={index}
                className="absolute inset-0"
                style={{
                  zIndex: hoveredIndex === index ? 1000 : sortedCategories.length - index,
                  opacity: hoveredIndex !== null && hoveredIndex !== index ? 0.6 : 1,
                  transition: 'opacity 0.3s ease'
                }}
              >
                <CircularProgress
                  percentage={category.score}
                  color={category.color}
                  size={280 - index * 40}
                  strokeWidth={20}
                  index={index}
                  category={category}
                  isHovered={hoveredIndex === index}
                />
              </div>
            ))}
          </div>

          {/* Right side - Legend */}
          <div className="flex-1 ml-12 space-y-6">
            {sortedCategories.map((category, index) => (
              <div
                key={index}
                className={`group cursor-pointer transition-all duration-300 p-3 rounded-lg ${
                  hoveredIndex === index
                    ? 'bg-gray-50 shadow-md transform scale-105'
                    : 'hover:bg-gray-50'
                }`}
                onMouseEnter={() => setHoveredIndex(index)}
                onMouseLeave={() => setHoveredIndex(null)}
              >
                <div className="flex items-center gap-4">
                  <div
                    className={`w-4 h-4 rounded-full transition-all duration-300 ${
                      hoveredIndex === index ? 'w-5 h-5 shadow-lg' : ''
                    }`}
                    style={{
                      backgroundColor: category.color,
                      boxShadow: hoveredIndex === index ? `0 0 10px ${category.color}40` : 'none'
                    }}
                  />
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div>
                        <span className={`text-sm font-medium uppercase tracking-wider transition-colors duration-300 ${
                          hoveredIndex === index ? 'text-gray-700' : 'text-gray-500'
                        }`}>
                          {category.shortName}
                        </span>
                        <p className={`text-xs mt-1 transition-colors duration-300 ${
                          hoveredIndex === index ? 'text-gray-600' : 'text-gray-400'
                        }`}>
                          {category.description}
                        </p>
                      </div>
                      <span
                        className={`text-2xl font-bold transition-all duration-300 ${
                          hoveredIndex === index ? 'text-3xl' : ''
                        }`}
                        style={{ color: category.color }}
                      >
                        {category.score}%
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Performance Insights */}
        <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
          <div className="flex items-start gap-3">
            <div className="p-2 bg-blue-100 rounded-lg flex-shrink-0">
              <TrendingUp className="w-4 h-4 text-blue-600" />
            </div>
            <div>
              <h4 className="text-sm font-semibold text-blue-900 mb-2">
                Performance Insight
              </h4>
              <p className="text-xs text-blue-700 leading-relaxed mb-3">
                {overallScore >= 80
                  ? "Anda menunjukkan performa yang sangat baik di berbagai aspek kehidupan. Kekuatan utama Anda terletak pada area yang ditampilkan di atas."
                  : overallScore >= 60
                  ? "Anda memiliki profil yang seimbang dengan beberapa area kekuatan yang menonjol. Fokuskan pengembangan pada area dengan skor tertinggi."
                  : "Anda memiliki potensi yang baik untuk dikembangkan. Pertimbangkan untuk fokus pada pengembangan area-area kekuatan yang sudah teridentifikasi."
                }
              </p>
              <div className="text-xs text-blue-600 bg-blue-100 rounded p-2">
                <p className="font-medium mb-1">Cara Perhitungan Skor:</p>
                <ul className="space-y-1 text-blue-700">
                  <li>• <strong>HABITS:</strong> Conscientiousness + Self-Regulation + Perseverance</li>
                  <li>• <strong>WORK:</strong> RIASEC dominan + Conscientiousness + Leadership + Perseverance</li>
                  <li>• <strong>HOME:</strong> Agreeableness + Stabilitas Emosi + Love + Kindness + Forgiveness</li>
                  <li>• <strong>SOCIAL:</strong> Extraversion + Agreeableness + Social Intelligence + Teamwork + Fairness</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
