'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '../ui/card';
import { Progress } from '../ui/progress';
import { AssessmentScores, getScoreInterpretation } from '../../types/assessment-results';
import { getDominantRiasecType, getTopViaStrengths } from '../../utils/assessment-calculations';
import { TrendingUp, BarChart3, Brain, Palette } from 'lucide-react';

interface VisualSummaryProps {
  scores: AssessmentScores;
}

export default function VisualSummary({ scores }: VisualSummaryProps) {
  // Get top categories from each assessment type
  const dominantRiasec = getDominantRiasecType(scores.riasec);
  const topViaStrengths = getTopViaStrengths(scores.viaIs, 1);
  
  // Get highest Big Five trait
  const oceanEntries = Object.entries(scores.ocean).sort(([,a], [,b]) => b - a);
  const topOceanTrait = oceanEntries[0];

  // Create top 3 categories for visualization
  const topCategories = [
    {
      name: `RIASEC: ${dominantRiasec.primary.charAt(0).toUpperCase() + dominantRiasec.primary.slice(1)}`,
      score: scores.riasec[dominantRiasec.primary as keyof typeof scores.riasec],
      color: '#6475e9',
      icon: BarChart3,
      type: 'RIASEC'
    },
    {
      name: `Big Five: ${topOceanTrait[0].charAt(0).toUpperCase() + topOceanTrait[0].slice(1)}`,
      score: topOceanTrait[1],
      color: '#22c55e',
      icon: Brain,
      type: 'Big Five'
    },
    {
      name: `VIA: ${topViaStrengths[0]?.strength.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()) || 'N/A'}`,
      score: topViaStrengths[0]?.score || 0,
      color: '#f59e0b',
      icon: Palette,
      type: 'VIA'
    }
  ];

  // Sort by score to show highest first
  const sortedCategories = topCategories.sort((a, b) => b.score - a.score);

  const CategoryBar = ({ 
    category, 
    index 
  }: { 
    category: typeof topCategories[0]; 
    index: number;
  }) => {
    const interpretation = getScoreInterpretation(category.score);
    const Icon = category.icon;
    
    return (
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div 
              className="p-2 rounded-lg"
              style={{ backgroundColor: category.color + '20' }}
            >
              <Icon className="w-4 h-4" style={{ color: category.color }} />
            </div>
            <div>
              <p className="font-medium text-gray-900 text-sm">{category.name}</p>
              <p className="text-xs text-gray-500">{category.type}</p>
            </div>
          </div>
          <div className="text-right">
            <p className="font-bold text-lg" style={{ color: category.color }}>
              {category.score}
            </p>
            <p className="text-xs text-gray-500">{interpretation.label}</p>
          </div>
        </div>
        
        <div className="space-y-2">
          <Progress 
            value={category.score} 
            className="h-3"
            style={{
              '--progress-background': category.color,
            } as React.CSSProperties}
          />
          <div className="flex justify-between text-xs text-gray-500">
            <span>0</span>
            <span>50</span>
            <span>100</span>
          </div>
        </div>
      </div>
    );
  };

  // Calculate overall performance
  const overallScore = Math.round(
    (sortedCategories.reduce((sum, cat) => sum + cat.score, 0)) / 3
  );
  const overallInterpretation = getScoreInterpretation(overallScore);

  return (
    <Card className="bg-white border-[#eaecf0]">
      <CardHeader className="pb-4">
        <div className="flex items-center gap-2">
          <div className="p-2 bg-[#e7eaff] rounded-lg">
            <TrendingUp className="w-6 h-6 text-[#6475e9]" />
          </div>
          <div>
            <CardTitle className="text-xl font-semibold text-[#1e1e1e]">
              Performance Summary
            </CardTitle>
            <p className="text-sm text-[#64707d]">
              Visualisasi kekuatan utama Anda
            </p>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Overall Score */}
        <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <div>
              <h3 className="font-semibold text-gray-900">Overall Performance</h3>
              <p className="text-sm text-gray-600">Rata-rata dari semua assessment</p>
            </div>
            <div className="text-right">
              <p className="text-2xl font-bold" style={{ color: overallInterpretation.color }}>
                {overallScore}
              </p>
              <p className="text-sm font-medium" style={{ color: overallInterpretation.color }}>
                {overallInterpretation.label}
              </p>
            </div>
          </div>
          <Progress 
            value={overallScore} 
            className="h-4"
            style={{
              '--progress-background': overallInterpretation.color,
            } as React.CSSProperties}
          />
        </div>

        {/* Top 3 Categories */}
        <div className="space-y-6">
          <h3 className="font-semibold text-gray-900 text-lg">Top 3 Strengths</h3>
          {sortedCategories.map((category, index) => (
            <div key={index}>
              <div className="flex items-center gap-2 mb-3">
                <div className="flex items-center justify-center w-6 h-6 bg-gray-200 text-gray-700 text-sm font-bold rounded-full">
                  {index + 1}
                </div>
                <span className="text-sm font-medium text-gray-600">
                  Rank #{index + 1}
                </span>
              </div>
              <CategoryBar category={category} index={index} />
            </div>
          ))}
        </div>

        {/* Performance Insights */}
        <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
          <div className="flex items-start gap-3">
            <div className="p-2 bg-blue-100 rounded-lg flex-shrink-0">
              <TrendingUp className="w-4 h-4 text-blue-600" />
            </div>
            <div>
              <h4 className="text-sm font-semibold text-blue-900 mb-1">
                Performance Insight
              </h4>
              <p className="text-xs text-blue-700 leading-relaxed">
                {overallScore >= 80 
                  ? "Anda menunjukkan performa yang sangat baik di berbagai aspek kepribadian. Kekuatan utama Anda terletak pada area yang ditampilkan di atas."
                  : overallScore >= 60
                  ? "Anda memiliki profil kepribadian yang seimbang dengan beberapa area kekuatan yang menonjol. Fokuskan pengembangan pada area dengan skor tertinggi."
                  : "Anda memiliki potensi yang baik untuk dikembangkan. Pertimbangkan untuk fokus pada pengembangan area-area kekuatan yang sudah teridentifikasi."
                }
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
